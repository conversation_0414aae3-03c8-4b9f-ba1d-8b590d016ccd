<script lang="ts" setup>
import { MinusSignSquareIcon, PlusSignSquareIcon } from 'hugeicons-vue'

// SEO and meta
useHead({
  title: 'FAQ - TicketPie',
  meta: [
    { name: 'description', content: 'Frequently asked questions about TicketPie - your gateway to unforgettable events.' },
  ],
})

// Reactive state for expanded FAQ items
const expandedFaq = ref<number | null>(null)

// FAQ structure interface
interface FAQ {
  questionKey: string
  answerKey: string
}

// Define FAQ structure with translation keys
const faqStructure: FAQ[] = [
  {
    questionKey: 'faq.general.pricing.question',
    answerKey: 'faq.general.pricing.answer',
  },
  {
    questionKey: 'faq.general.setup_time.question',
    answerKey: 'faq.general.setup_time.answer',
  },
  {
    questionKey: 'faq.general.payment_methods.question',
    answerKey: 'faq.general.payment_methods.answer',
  },
  {
    questionKey: 'faq.general.support.question',
    answerKey: 'faq.general.support.answer',
  },
  {
    questionKey: 'faq.general.availability.question',
    answerKey: 'faq.general.availability.answer',
  },
  {
    questionKey: 'faq.organizers.getting_started.question',
    answerKey: 'faq.organizers.getting_started.answer',
  },
  {
    questionKey: 'faq.organizers.ticket_types.question',
    answerKey: 'faq.organizers.ticket_types.answer',
  },
  {
    questionKey: 'faq.organizers.analytics.question',
    answerKey: 'faq.organizers.analytics.answer',
  },
  {
    questionKey: 'faq.attendees.buying_tickets.question',
    answerKey: 'faq.attendees.buying_tickets.answer',
  },
  {
    questionKey: 'faq.attendees.refunds.question',
    answerKey: 'faq.attendees.refunds.answer',
  },
  {
    questionKey: 'faq.technical.mobile_app.question',
    answerKey: 'faq.technical.mobile_app.answer',
  },
  {
    questionKey: 'faq.technical.integrations.question',
    answerKey: 'faq.technical.integrations.answer',
  },
]

// FAQ categories for better organization
const faqCategories = [
  {
    titleKey: 'faq.categories.general.title',
    items: faqStructure.slice(0, 5),
  },
  {
    titleKey: 'faq.categories.organizers.title',
    items: faqStructure.slice(5, 8),
  },
  {
    titleKey: 'faq.categories.attendees.title',
    items: faqStructure.slice(8, 10),
  },
  {
    titleKey: 'faq.categories.technical.title',
    items: faqStructure.slice(10, 12),
  },
]

// Toggle FAQ item
function toggleFaq(index: number) {
  expandedFaq.value = expandedFaq.value === index ? null : index
}
</script>

<template>
  <div class="min-h-screen bg-pie-25">
    <!-- Hero Section -->
    <div class="w-full px-4 py-12 md:py-24">
      <div class="flex flex-col justify-center items-center gap-6 border border-slate-700 bg-white lg:rounded-[3rem] md:rounded-[2rem] rounded-3xl shadow-lg lg:py-24 md:py-20 py-12 px-4 md:px-6 lg:px-8 w-full max-w-[95vw] lg:max-w-[90rem] mx-auto">
        <div class="flex flex-col gap-6 justify-center items-center text-center max-w-4xl">
          <h1 class="md:text-8xl text-4xl font-sofia font-[800] text-pie-700 spacing-desktop-5 spacing-mobile-3">
            {{ $t('faq.hero.title') }}
          </h1>
          <p class="md:text-xl-medium text-sm-medium text-slate-700 max-w-2xl">
            {{ $t('faq.hero.description') }}
          </p>
        </div>
      </div>
    </div>

    <!-- FAQ Categories -->
    <div class="w-full px-4 pb-12 md:pb-24">
      <div class="faq-container mx-auto">
        <div
          v-for="(category, categoryIndex) in faqCategories"
          :key="categoryIndex"
          class="mb-12 md:mb-16 faq-item"
        >
          <!-- Category Title -->
          <div class="mb-8 w-full">
            <h2 class="md:text-4xl text-2xl font-sofia font-[700] text-pie-700 text-center md:text-left">
              {{ $t(category.titleKey) }}
            </h2>
          </div>

          <!-- FAQ Items in Category -->
          <div class="faq-item border border-slate-700 bg-white lg:rounded-[2rem] md:rounded-[1.5rem] rounded-2xl shadow-lg overflow-hidden">
            <div
              v-for="(faq, index) in category.items"
              :key="`${categoryIndex}-${index}`"
              class="faq-item border-b border-slate-200 last:border-b-0"
            >
              <button
                class="faq-button py-6 md:py-8 px-6 md:px-8 text-left hover:bg-slate-50 transition-colors duration-200 group"
                @click="toggleFaq(categoryIndex * 100 + index)"
              >
                <div class="faq-question">
                  <p class="md:text-xl-medium text-base-medium text-slate-900 group-hover:text-pie-700 transition-colors duration-200 faq-content">
                    {{ $t(faq.questionKey) }}
                  </p>
                </div>
                <div class="faq-icon">
                  <component
                    :is="expandedFaq === (categoryIndex * 100 + index) ? MinusSignSquareIcon : PlusSignSquareIcon"
                    :size="24"
                    class="text-slate-600 group-hover:text-pie-700 stroke-2 transition-colors duration-200"
                  />
                </div>
              </button>
              <div
                v-if="expandedFaq === (categoryIndex * 100 + index)"
                class="faq-item px-6 md:px-8 pb-6 md:pb-8 transition-all duration-300 ease-in-out"
              >
                <div class="faq-item border-l-4 border-pie-300 pl-4 md:pl-6">
                  <div class="faq-item">
                    <p class="md:text-lg-normal text-sm-normal text-slate-600 leading-relaxed faq-content">
                      {{ $t(faq.answerKey) }}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Section -->
    <div class="w-full px-4 pb-12 md:pb-24">
      <div class="flex flex-col justify-center items-center gap-6 border border-slate-700 bg-white lg:rounded-[3rem] md:rounded-[2rem] rounded-3xl shadow-lg lg:py-24 md:py-20 py-12 px-4 md:px-6 lg:px-8 w-full max-w-[95vw] lg:max-w-[90rem] mx-auto">
        <div class="flex flex-col gap-6 justify-center items-center text-center max-w-4xl">
          <h2 class="md:text-6xl text-3xl font-sofia font-[700] text-pie-700">
            {{ $t('faq.contact.title') }}
          </h2>
          <p class="md:text-lg-normal text-sm-normal text-slate-600 max-w-2xl">
            {{ $t('faq.contact.description') }}
          </p>
          <div class="flex flex-col md:flex-row gap-4 mt-4">
            <NexButton
              to="/contact"
              variant="primary"
              text-key="faq.contact.buttons.contact_us"
              :paddingx="8"
              :paddingy="4"
            />
            <NexButton
              to="/help_center"
              variant="secondary"
              text-key="faq.contact.buttons.help_center"
              :paddingx="8"
              :paddingy="4"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Custom spacing classes for consistent typography */
.spacing-desktop-5 {
  letter-spacing: -0.05em;
}

.spacing-mobile-3 {
  letter-spacing: -0.03em;
}

@media (max-width: 768px) {
  .spacing-desktop-5 {
    letter-spacing: -0.03em;
  }
}

/* FAQ width stability fixes */
.faq-container {
  width: 100%;
  max-width: 95vw;
  box-sizing: border-box;
}

@media (min-width: 1024px) {
  .faq-container {
    max-width: 90rem;
  }
}

.faq-item {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.faq-content {
  width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  box-sizing: border-box;
}

.faq-button {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.faq-question {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  padding-right: 1rem;
}

.faq-icon {
  flex-shrink: 0;
  margin-left: 0.5rem;
}
</style>
